import { BlogPostGrid } from '@/components/blog-post-card';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import PublicLayout from '@/layouts/public-layout';
import { type BlogPost } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { ArrowRight, Calendar, Clock, Eye, Tag } from 'lucide-react';
import { formatDate, getInitials } from '@/lib/formatters';

interface BlogPostProps {
    post: BlogPost;
    relatedPosts: BlogPost[];
    meta: {
        title: string;
        description: string;
        keywords?: string;
        canonical: string;
        og_image?: string;
        published_time?: string;
        modified_time?: string;
        author?: string;
    };
}

export default function BlogPostDetail({ post, relatedPosts, meta }: BlogPostProps) {

    return (
        <PublicLayout>
            <Head
                title={meta.title}
                description={meta.description}
            >
                <meta name="keywords" content={meta.keywords} />
                <meta property="og:title" content={meta.title} />
                <meta property="og:description" content={meta.description} />
                {meta.og_image && <meta property="og:image" content={meta.og_image} />}
                {meta.published_time && <meta property="article:published_time" content={meta.published_time} />}
                {meta.modified_time && <meta property="article:modified_time" content={meta.modified_time} />}
                {meta.author && <meta property="article:author" content={meta.author} />}
                <link rel="canonical" href={meta.canonical} />
            </Head>

            {/* Hero Section */}
            <section className="py-16 bg-gradient-to-br from-background via-background to-muted/20">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto">
                        <div className="text-center mb-8">
                            {post.category && (
                                <Badge variant="secondary" className="mb-4">
                                    <Tag className="h-3 w-3 mr-1" />
                                    {post.category.name}
                                </Badge>
                            )}
                            <h1 className="text-4xl font-bold mb-6 sm:text-5xl leading-tight">
                                {post.title}
                            </h1>
                            <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
                                {post.excerpt}
                            </p>
                            
                            {/* Article Meta */}
                            <div className="flex flex-wrap items-center justify-center gap-6 text-sm text-muted-foreground">
                                {post.author && (
                                    <div className="flex items-center space-x-2">
                                        <Avatar className="h-8 w-8">
                                            <AvatarImage src={`/images/team/${post.author.name.toLowerCase().replace(' ', '-')}.jpg`} />
                                            <AvatarFallback>
                                                {getInitials(post.author.name)}
                                            </AvatarFallback>
                                        </Avatar>
                                        <span>By {post.author.name}</span>
                                    </div>
                                )}
                                <div className="flex items-center space-x-1">
                                    <Calendar className="h-4 w-4" />
                                    <span>{formatDate(post.published_at)}</span>
                                </div>
                                <div className="flex items-center space-x-1">
                                    <Clock className="h-4 w-4" />
                                    <span>{post.reading_time || 5} min read</span>
                                </div>
                                <div className="flex items-center space-x-1">
                                    <Eye className="h-4 w-4" />
                                    <span>{post.views_count?.toLocaleString() || 0} views</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Featured Image */}
            {post.featured_image && (
                <section className="py-8">
                    <div className="container mx-auto px-4">
                        <div className="max-w-4xl mx-auto">
                            <img
                                src={post.featured_image}
                                alt={post.title}
                                className="w-full h-64 md:h-96 object-cover rounded-lg shadow-lg"
                            />
                        </div>
                    </div>
                </section>
            )}

            {/* Article Content */}
            <section className="py-16">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto">
                        <div 
                            className="prose prose-lg max-w-none prose-headings:text-foreground prose-p:text-muted-foreground prose-strong:text-foreground prose-ul:text-muted-foreground prose-ol:text-muted-foreground"
                            dangerouslySetInnerHTML={{ __html: post.content }}
                        />
                    </div>
                </div>
            </section>

            {/* Author Bio */}
            {post.author && (
                <section className="py-16 bg-muted/30">
                    <div className="container mx-auto px-4">
                        <div className="max-w-4xl mx-auto">
                            <Card className="border-0 shadow-md">
                                <CardContent className="p-8">
                                    <div className="flex items-start space-x-4">
                                        <Avatar className="h-16 w-16">
                                            <AvatarImage src={`/images/team/${post.author.name.toLowerCase().replace(' ', '-')}.jpg`} />
                                            <AvatarFallback className="text-lg">
                                                {getInitials(post.author.name)}
                                            </AvatarFallback>
                                        </Avatar>
                                        <div className="flex-1">
                                            <h3 className="text-xl font-semibold mb-2">About {post.author.name}</h3>
                                            <p className="text-muted-foreground mb-4">
                                                {post.author.name} is a digital marketing specialist at ConvertOKit,
                                                focusing on Meta advertising and conversion optimization. With years of experience 
                                                in the industry, they help businesses maximize their advertising ROI through 
                                                strategic campaign management and advanced tracking implementation.
                                            </p>
                                            <Button variant="outline" size="sm" asChild>
                                                <Link href="/team">
                                                    View Profile
                                                    <ArrowRight className="ml-2 h-4 w-4" />
                                                </Link>
                                            </Button>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </section>
            )}

            {/* Related Posts */}
            {relatedPosts.length > 0 && (
                <section className="py-16">
                    <div className="container mx-auto px-4">
                        <div className="max-w-6xl mx-auto">
                            <h2 className="text-3xl font-bold text-center mb-12">Related Articles</h2>
                            <BlogPostGrid posts={relatedPosts} />
                        </div>
                    </div>
                </section>
            )}

            {/* CTA Section */}
            <section className="py-16 bg-primary text-primary-foreground">
                <div className="container mx-auto px-4 text-center">
                    <h2 className="text-3xl font-bold mb-4">Need Help With Your Meta Advertising?</h2>
                    <p className="text-lg mb-8 opacity-90 max-w-2xl mx-auto">
                        Let our experts help you implement these strategies and optimize your campaigns for better results
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Button size="lg" variant="secondary" asChild>
                            <Link href="/book-consultation">
                                Book Free Consultation
                                <ArrowRight className="ml-2 h-5 w-5" />
                            </Link>
                        </Button>
                        <Button size="lg" variant="outline" className="border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary" asChild>
                            <Link href="/blog">
                                Read More Articles
                            </Link>
                        </Button>
                    </div>
                </div>
            </section>
        </PublicLayout>
    );
}
