import { HeroSection } from '@/components/hero-section';
import { ServiceGrid } from '@/components/service-card';
import { TeamGrid } from '@/components/team-member-card';
import { BlogPostGrid } from '@/components/blog-post-card';
import { Button } from '@/components/ui/button';
import SeoHead from '@/components/seo-head';

import PublicLayout from '@/layouts/public-layout';
import { type Service, type TeamMember, type BlogPost } from '@/types';
import { Link } from '@inertiajs/react';
import { ArrowRight, TrendingUp, Users, Zap } from 'lucide-react';

interface HomeProps {
    services: Service[];
    teamMembers: TeamMember[];
    blogPosts: BlogPost[];
    meta: any;
    structuredData?: any;
}

export default function Home({ services, teamMembers, blogPosts, meta, structuredData }: HomeProps) {

    return (
        <PublicLayout>
            <SeoHead meta={meta} structuredData={structuredData} />

            {/* Hero Section */}
            <HeroSection />

            {/* About Section */}
            <section className="py-16 bg-muted/30">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto text-center">
                        <h2 className="text-3xl font-bold mb-6">About ConvertOKit</h2>
                        <p className="text-lg text-muted-foreground mb-8 leading-relaxed">
                            We're a team of digital marketing specialists focused on Meta advertising and web analytics. 
                            Our mission is to help businesses maximize their digital marketing ROI through expert campaign 
                            management, advanced tracking implementation, and data-driven optimization strategies.
                        </p>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div className="text-center">
                                <TrendingUp className="h-12 w-12 text-primary mx-auto mb-4" />
                                <h3 className="font-semibold mb-2">Performance Focused</h3>
                                <p className="text-sm text-muted-foreground">
                                    Every strategy is designed to maximize your return on ad spend
                                </p>
                            </div>
                            <div className="text-center">
                                <Users className="h-12 w-12 text-primary mx-auto mb-4" />
                                <h3 className="font-semibold mb-2">Expert Team</h3>
                                <p className="text-sm text-muted-foreground">
                                    Certified professionals with years of experience in digital marketing
                                </p>
                            </div>
                            <div className="text-center">
                                <Zap className="h-12 w-12 text-primary mx-auto mb-4" />
                                <h3 className="font-semibold mb-2">Advanced Tracking</h3>
                                <p className="text-sm text-muted-foreground">
                                    Cutting-edge analytics implementation for accurate data insights
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Services Section */}
            <section className="py-16">
                <div className="container mx-auto px-4">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl font-bold mb-4">Our Services</h2>
                        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                            Comprehensive digital marketing solutions to grow your business and track your success
                        </p>
                    </div>
                    <ServiceGrid services={services} />
                    <div className="text-center mt-8">
                        <Button size="lg" asChild>
                            <Link href="/services">
                                View All Services
                                <ArrowRight className="ml-2 h-5 w-5" />
                            </Link>
                        </Button>
                    </div>
                </div>
            </section>

            {/* Team Section */}
            {teamMembers.length > 0 && (
                <section className="py-16 bg-muted/30">
                    <div className="container mx-auto px-4">
                        <div className="text-center mb-12">
                            <h2 className="text-3xl font-bold mb-4">Meet Our Team</h2>
                            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                                Our experienced professionals are here to help you succeed
                            </p>
                        </div>
                        <TeamGrid members={teamMembers} />
                        <div className="text-center mt-8">
                            <Button variant="outline" size="lg" asChild>
                                <Link href="/team">
                                    Meet the Full Team
                                    <ArrowRight className="ml-2 h-5 w-5" />
                                </Link>
                            </Button>
                        </div>
                    </div>
                </section>
            )}

            {/* Blog Section */}
            {blogPosts.length > 0 && (
                <section className="py-16">
                    <div className="container mx-auto px-4">
                        <div className="text-center mb-12">
                            <h2 className="text-3xl font-bold mb-4">Latest Insights</h2>
                            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                                Stay updated with the latest trends and strategies in digital marketing
                            </p>
                        </div>
                        <BlogPostGrid posts={blogPosts} />
                        <div className="text-center mt-8">
                            <Button variant="outline" size="lg" asChild>
                                <Link href="/blog">
                                    Read More Articles
                                    <ArrowRight className="ml-2 h-5 w-5" />
                                </Link>
                            </Button>
                        </div>
                    </div>
                </section>
            )}

            {/* CTA Section */}
            <section className="py-16 bg-primary text-primary-foreground">
                <div className="container mx-auto px-4 text-center">
                    <h2 className="text-3xl font-bold mb-4">Ready to Boost Your Digital Marketing?</h2>
                    <p className="text-lg mb-8 opacity-90 max-w-2xl mx-auto">
                        Let's discuss your goals and create a customized strategy to maximize your ROI
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Button size="lg" variant="secondary" asChild>
                            <Link href="/book-consultation">
                                Book Free Consultation
                                <ArrowRight className="ml-2 h-5 w-5" />
                            </Link>
                        </Button>
                        <Button size="lg" variant="outline" className="border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary" asChild>
                            <Link href="/contact">
                                Get In Touch
                            </Link>
                        </Button>
                    </div>
                </div>
            </section>
        </PublicLayout>
    );
}
