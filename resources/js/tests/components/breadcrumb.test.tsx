import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import Breadcrumb from '@/components/breadcrumb';

describe('Breadcrumb Component', () => {
  const mockBreadcrumbs = [
    { name: 'Home', url: '/' },
    { name: 'Services', url: '/services' },
    { name: 'Meta Ads Management', url: '/services/meta-ads-management' },
  ];

  it('renders all breadcrumb items correctly', () => {
    render(<Breadcrumb breadcrumbs={mockBreadcrumbs} />);

    expect(screen.getByText('Home')).toBeInTheDocument();
    expect(screen.getByText('Services')).toBeInTheDocument();
    expect(screen.getByText('Meta Ads Management')).toBeInTheDocument();
  });

  it('renders breadcrumb links with correct href attributes', () => {
    render(<Breadcrumb breadcrumbs={mockBreadcrumbs} />);

    const homeLink = screen.getByRole('link', { name: 'Home' });
    const servicesLink = screen.getByRole('link', { name: 'Services' });

    expect(homeLink).toHaveAttribute('href', '/');
    expect(servicesLink).toHaveAttribute('href', '/services');
  });

  it('renders the last breadcrumb item as text (not a link)', () => {
    render(<Breadcrumb breadcrumbs={mockBreadcrumbs} />);

    const lastItem = screen.getByText('Meta Ads Management');
    expect(lastItem.tagName).toBe('SPAN');
    expect(lastItem).not.toHaveAttribute('href');
  });

  it('renders separators between breadcrumb items', () => {
    const { container } = render(<Breadcrumb breadcrumbs={mockBreadcrumbs} />);

    const separators = container.querySelectorAll('svg');
    // Should have 2 separators for 3 breadcrumb items
    expect(separators).toHaveLength(2);
  });

  it('applies correct CSS classes for styling', () => {
    const { container } = render(<Breadcrumb breadcrumbs={mockBreadcrumbs} />);

    const breadcrumbNav = container.querySelector('nav');
    expect(breadcrumbNav).toHaveClass('flex', 'items-center', 'space-x-2', 'text-sm');

    const links = container.querySelectorAll('a');
    links.forEach(link => {
      expect(link).toHaveClass('text-muted-foreground', 'hover:text-foreground', 'transition-colors');
    });

    const lastItem = container.querySelector('span:last-child');
    expect(lastItem).toHaveClass('text-foreground', 'font-medium');
  });

  it('handles single breadcrumb item correctly', () => {
    const singleBreadcrumb = [{ name: 'Home', url: '/' }];
    const { container } = render(<Breadcrumb breadcrumbs={singleBreadcrumb} />);

    expect(screen.getByText('Home')).toBeInTheDocument();
    
    // Should not have any separators
    const separators = container.querySelectorAll('svg');
    expect(separators).toHaveLength(0);

    // Single item should be rendered as text (current page)
    const homeItem = screen.getByText('Home');
    expect(homeItem.tagName).toBe('SPAN');
  });

  it('handles empty breadcrumbs array gracefully', () => {
    const { container } = render(<Breadcrumb breadcrumbs={[]} />);

    const breadcrumbNav = container.querySelector('nav');
    expect(breadcrumbNav).toBeInTheDocument();
    expect(breadcrumbNav?.children).toHaveLength(0);
  });

  it('renders with proper accessibility attributes', () => {
    render(<Breadcrumb breadcrumbs={mockBreadcrumbs} />);

    const nav = screen.getByRole('navigation');
    expect(nav).toHaveAttribute('aria-label', 'Breadcrumb');

    const list = screen.getByRole('list');
    expect(list).toBeInTheDocument();

    const listItems = screen.getAllByRole('listitem');
    expect(listItems).toHaveLength(3);
  });

  it('handles long breadcrumb names correctly', () => {
    const longBreadcrumbs = [
      { name: 'Home', url: '/' },
      { name: 'Very Long Service Category Name That Might Wrap', url: '/services' },
      { name: 'Extremely Long Service Name That Definitely Should Wrap To Multiple Lines', url: '/services/long-service' },
    ];

    render(<Breadcrumb breadcrumbs={longBreadcrumbs} />);

    expect(screen.getByText('Very Long Service Category Name That Might Wrap')).toBeInTheDocument();
    expect(screen.getByText('Extremely Long Service Name That Definitely Should Wrap To Multiple Lines')).toBeInTheDocument();
  });

  it('handles special characters in breadcrumb names', () => {
    const specialCharBreadcrumbs = [
      { name: 'Home', url: '/' },
      { name: 'Services & Solutions', url: '/services' },
      { name: 'Meta Ads (Facebook & Instagram)', url: '/services/meta-ads' },
    ];

    render(<Breadcrumb breadcrumbs={specialCharBreadcrumbs} />);

    expect(screen.getByText('Services & Solutions')).toBeInTheDocument();
    expect(screen.getByText('Meta Ads (Facebook & Instagram)')).toBeInTheDocument();
  });

  it('renders with correct ARIA current attribute for last item', () => {
    render(<Breadcrumb breadcrumbs={mockBreadcrumbs} />);

    const lastItem = screen.getByText('Meta Ads Management');
    expect(lastItem).toHaveAttribute('aria-current', 'page');
  });

  it('uses ChevronRight icon for separators', () => {
    const { container } = render(<Breadcrumb breadcrumbs={mockBreadcrumbs} />);

    const separators = container.querySelectorAll('svg');
    separators.forEach(separator => {
      expect(separator).toHaveClass('h-4', 'w-4', 'text-muted-foreground');
    });
  });
});
