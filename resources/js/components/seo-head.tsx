import { Head } from '@inertiajs/react';

interface SeoHeadProps {
    meta: {
        title: string;
        description: string;
        keywords?: string;
        canonical: string;
        og_title?: string;
        og_description?: string;
        og_type?: string;
        og_url?: string;
        og_site_name?: string;
        og_image?: string;
        twitter_card?: string;
        twitter_title?: string;
        twitter_description?: string;
        twitter_image?: string;
        article_published_time?: string;
        article_modified_time?: string;
        article_author?: string;
    };
    structuredData?: any | any[];
    breadcrumbs?: Array<{
        name: string;
        url: string;
    }>;
    robots?: string;
}

export default function SeoHead({ 
    meta, 
    structuredData, 
    breadcrumbs, 
    robots = 'index, follow' 
}: SeoHeadProps) {
    // Ensure structured data is an array
    const structuredDataArray = Array.isArray(structuredData) ? structuredData : [structuredData].filter(Boolean);

    return (
        <Head title={meta.title}>
            {/* Basic Meta Tags */}
            <meta name="description" content={meta.description} />
            {meta.keywords && <meta name="keywords" content={meta.keywords} />}
            <meta name="robots" content={robots} />
            <link rel="canonical" href={meta.canonical} />

            {/* Open Graph Tags */}
            <meta property="og:title" content={meta.og_title || meta.title} />
            <meta property="og:description" content={meta.og_description || meta.description} />
            <meta property="og:type" content={meta.og_type || 'website'} />
            <meta property="og:url" content={meta.og_url || meta.canonical} />
            <meta property="og:site_name" content={meta.og_site_name || 'ConvertOKit'} />
            {meta.og_image && <meta property="og:image" content={meta.og_image} />}

            {/* Twitter Card Tags */}
            <meta name="twitter:card" content={meta.twitter_card || 'summary_large_image'} />
            <meta name="twitter:title" content={meta.twitter_title || meta.title} />
            <meta name="twitter:description" content={meta.twitter_description || meta.description} />
            {meta.twitter_image && <meta name="twitter:image" content={meta.twitter_image} />}

            {/* Article Meta Tags */}
            {meta.article_published_time && (
                <meta property="article:published_time" content={meta.article_published_time} />
            )}
            {meta.article_modified_time && (
                <meta property="article:modified_time" content={meta.article_modified_time} />
            )}
            {meta.article_author && (
                <meta property="article:author" content={meta.article_author} />
            )}

            {/* Structured Data */}
            {structuredDataArray.map((data, index) => (
                <script
                    key={`structured-data-${index}`}
                    type="application/ld+json"
                    dangerouslySetInnerHTML={{
                        __html: JSON.stringify(data)
                    }}
                />
            ))}

            {/* Additional SEO Tags */}
            <meta name="viewport" content="width=device-width, initial-scale=1" />
            <meta httpEquiv="Content-Type" content="text/html; charset=utf-8" />
            <meta name="language" content="English" />
            <meta name="author" content="ConvertOKit" />
            
            {/* Favicon and Icons */}
            <link rel="icon" type="image/x-icon" href="/favicon.ico" />
            <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
            <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
            <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
            <link rel="manifest" href="/site.webmanifest" />
        </Head>
    );
}

interface BreadcrumbProps {
    breadcrumbs: Array<{
        name: string;
        url: string;
    }>;
    className?: string;
}

export function Breadcrumb({ breadcrumbs, className = '' }: BreadcrumbProps) {
    if (!breadcrumbs || breadcrumbs.length <= 1) {
        return null;
    }

    return (
        <nav className={`flex ${className}`} aria-label="Breadcrumb">
            <ol className="inline-flex items-center space-x-1 md:space-x-3">
                {breadcrumbs.map((breadcrumb, index) => (
                    <li key={index} className="inline-flex items-center">
                        {index > 0 && (
                            <svg
                                className="w-3 h-3 text-gray-400 mx-1"
                                aria-hidden="true"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 6 10"
                            >
                                <path
                                    stroke="currentColor"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="2"
                                    d="m1 9 4-4-4-4"
                                />
                            </svg>
                        )}
                        {index === breadcrumbs.length - 1 ? (
                            <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                                {breadcrumb.name}
                            </span>
                        ) : (
                            <a
                                href={breadcrumb.url}
                                className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"
                            >
                                {breadcrumb.name}
                            </a>
                        )}
                    </li>
                ))}
            </ol>
        </nav>
    );
}
