<?php

namespace Tests\Unit;

use App\Services\ServiceService;
use App\Models\Service;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ServiceServiceTest extends TestCase
{
    use RefreshDatabase;

    private ServiceService $serviceService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->serviceService = new ServiceService();
    }

    public function test_get_active_services_returns_only_active_services()
    {
        Service::factory()->create(['title' => 'Active Service', 'is_active' => true]);
        Service::factory()->create(['title' => 'Inactive Service', 'is_active' => false]);

        $services = $this->serviceService->getActiveServices();

        $this->assertCount(1, $services);
        $this->assertEquals('Active Service', $services->first()->title);
    }

    public function test_get_active_services_with_search_filter()
    {
        Service::factory()->create([
            'title' => 'Meta Ads Management',
            'description' => 'Professional Meta advertising',
            'is_active' => true
        ]);
        Service::factory()->create([
            'title' => 'Facebook Pixel Setup',
            'description' => 'Professional pixel implementation',
            'is_active' => true
        ]);
        Service::factory()->create([
            'title' => 'Google Analytics Setup',
            'description' => 'Professional GA4 implementation',
            'is_active' => true
        ]);

        $services = $this->serviceService->getActiveServices(['search' => 'Meta']);

        $this->assertCount(1, $services);
        $this->assertEquals('Meta Ads Management', $services->first()->title);
    }

    public function test_get_active_services_with_category_filter()
    {
        Service::factory()->create([
            'title' => 'Meta Ads Management',
            'category' => 'Meta Advertising',
            'is_active' => true
        ]);
        Service::factory()->create([
            'title' => 'Facebook Pixel Setup',
            'category' => 'Tracking & Analytics',
            'is_active' => true
        ]);

        $services = $this->serviceService->getActiveServices(['category' => 'Meta Advertising']);

        $this->assertCount(1, $services);
        $this->assertEquals('Meta Ads Management', $services->first()->title);
    }

    public function test_get_active_services_with_sorting()
    {
        Service::factory()->create([
            'title' => 'Z Service',
            'sort_order' => 3,
            'is_active' => true
        ]);
        Service::factory()->create([
            'title' => 'A Service',
            'sort_order' => 1,
            'is_active' => true
        ]);
        Service::factory()->create([
            'title' => 'M Service',
            'sort_order' => 2,
            'is_active' => true
        ]);

        // Test sort by sort_order ascending (default)
        $services = $this->serviceService->getActiveServices([
            'sort_by' => 'sort_order',
            'sort_direction' => 'asc'
        ]);

        $this->assertEquals('A Service', $services->first()->title);
        $this->assertEquals('Z Service', $services->last()->title);

        // Test sort by title ascending
        $services = $this->serviceService->getActiveServices([
            'sort_by' => 'title',
            'sort_direction' => 'asc'
        ]);

        $this->assertEquals('A Service', $services->first()->title);
        $this->assertEquals('Z Service', $services->last()->title);

        // Test sort by title descending
        $services = $this->serviceService->getActiveServices([
            'sort_by' => 'title',
            'sort_direction' => 'desc'
        ]);

        $this->assertEquals('Z Service', $services->first()->title);
        $this->assertEquals('A Service', $services->last()->title);
    }

    public function test_get_active_services_with_limit()
    {
        Service::factory()->count(5)->create(['is_active' => true]);

        $services = $this->serviceService->getActiveServices(['limit' => 3]);

        $this->assertCount(3, $services);
    }

    public function test_get_active_services_with_pagination()
    {
        Service::factory()->count(15)->create(['is_active' => true]);

        $services = $this->serviceService->getActiveServices([
            'paginate' => true,
            'limit' => 10
        ]);

        $this->assertInstanceOf(\Illuminate\Pagination\LengthAwarePaginator::class, $services);
        $this->assertEquals(10, $services->perPage());
        $this->assertEquals(15, $services->total());
        $this->assertEquals(2, $services->lastPage());
    }

    public function test_find_by_slug_returns_correct_service()
    {
        $service = Service::factory()->create([
            'title' => 'Test Service',
            'slug' => 'test-service',
            'is_active' => true
        ]);

        $foundService = $this->serviceService->findBySlug('test-service');

        $this->assertNotNull($foundService);
        $this->assertEquals('Test Service', $foundService->title);
        $this->assertEquals('test-service', $foundService->slug);
    }

    public function test_find_by_slug_returns_null_for_inactive_service()
    {
        Service::factory()->create([
            'title' => 'Inactive Service',
            'slug' => 'inactive-service',
            'is_active' => false
        ]);

        $foundService = $this->serviceService->findBySlug('inactive-service');

        $this->assertNull($foundService);
    }

    public function test_find_by_slug_returns_null_for_non_existent_service()
    {
        $foundService = $this->serviceService->findBySlug('non-existent-service');

        $this->assertNull($foundService);
    }

    public function test_get_active_services_with_multiple_filters()
    {
        Service::factory()->create([
            'title' => 'Meta Ads Management',
            'description' => 'Professional Meta advertising',
            'category' => 'Meta Advertising',
            'sort_order' => 1,
            'is_active' => true
        ]);
        Service::factory()->create([
            'title' => 'Meta Pixel Setup',
            'description' => 'Professional Meta pixel implementation',
            'category' => 'Meta Advertising',
            'sort_order' => 2,
            'is_active' => true
        ]);
        Service::factory()->create([
            'title' => 'Facebook Analytics',
            'description' => 'Professional Facebook analytics',
            'category' => 'Tracking & Analytics',
            'sort_order' => 3,
            'is_active' => true
        ]);

        $services = $this->serviceService->getActiveServices([
            'search' => 'Meta',
            'category' => 'Meta Advertising',
            'sort_by' => 'sort_order',
            'sort_direction' => 'asc',
            'limit' => 5
        ]);

        $this->assertCount(2, $services);
        $this->assertEquals('Meta Ads Management', $services->first()->title);
        $this->assertEquals('Meta Pixel Setup', $services->last()->title);
    }

    public function test_get_active_services_search_in_description()
    {
        Service::factory()->create([
            'title' => 'Service One',
            'description' => 'This service includes Meta advertising features',
            'is_active' => true
        ]);
        Service::factory()->create([
            'title' => 'Service Two',
            'description' => 'This service includes Google Analytics features',
            'is_active' => true
        ]);

        $services = $this->serviceService->getActiveServices(['search' => 'Meta']);

        $this->assertCount(1, $services);
        $this->assertEquals('Service One', $services->first()->title);
    }

    public function test_get_active_services_case_insensitive_search()
    {
        Service::factory()->create([
            'title' => 'Meta Ads Management',
            'description' => 'Professional Meta advertising',
            'is_active' => true
        ]);

        $services = $this->serviceService->getActiveServices(['search' => 'meta']);

        $this->assertCount(1, $services);
        $this->assertEquals('Meta Ads Management', $services->first()->title);
    }

    public function test_get_active_services_returns_empty_collection_when_no_matches()
    {
        Service::factory()->create([
            'title' => 'Meta Ads Management',
            'category' => 'Meta Advertising',
            'is_active' => true
        ]);

        $services = $this->serviceService->getActiveServices(['search' => 'NonExistent']);

        $this->assertCount(0, $services);
    }

    public function test_get_active_services_with_invalid_sort_field_uses_default()
    {
        Service::factory()->create([
            'title' => 'Test Service',
            'sort_order' => 1,
            'is_active' => true
        ]);

        $services = $this->serviceService->getActiveServices([
            'sort_by' => 'invalid_field',
            'sort_direction' => 'asc'
        ]);

        // Should still return results using default sorting
        $this->assertCount(1, $services);
    }
}
